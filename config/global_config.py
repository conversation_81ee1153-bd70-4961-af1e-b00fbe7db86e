"""
Global Configuration Manager for Social Media Monitoring Tool
Manages scraping timing, account lists, and global settings
"""

import json
import logging
import os
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class GlobalConfigManager:
    def __init__(self, config_file: str = "config/scraping_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_file}")
                return config
            else:
                logger.info("Creating default configuration")
                return self._create_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}. Using defaults.")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration"""
        default_config = {
            "version": "1.0",
            "last_updated": datetime.now().isoformat(),
            "global_settings": {
                "enabled": True,
                "max_posts_per_account": 5,
                "data_retention_days": 7,
                "log_level": "INFO"
            },
            "timing": {
                "delay_between_accounts": 3.0,
                "delay_between_platforms": 15.0,
                "delay_after_all_platforms_complete": 30.0,
                "delay_between_posts": 0.5,
                "random_delay_factor": 0.3,
                "scraping_interval_minutes": 5
            },
            "platforms": {
                "twitter": {
                    "enabled": True,
                    "accounts": [
                        "BBCBreaking",
                        "CNN",
                        "Reuters", 
                        "nytimes",
                        "washingtonpost"
                    ],
                    "custom_timing": {
                        "delay_between_accounts": 3.0,
                        "delay_after_platform_complete": 10.0
                    }
                },
                "facebook": {
                    "enabled": True,
                    "pages": [
                        "BBC",
                        "CNN",
                        "Reuters",
                        "TheNewYorkTimes", 
                        "washingtonpost"
                    ],
                    "custom_timing": {
                        "delay_between_accounts": 2.0,
                        "delay_after_platform_complete": 10.0
                    }
                }
            },
            "dashboards": []
        }
        
        # Save default config
        self.save_config(default_config)
        return default_config
    
    def save_config(self, config: Optional[Dict[str, Any]] = None):
        """Save configuration to file"""
        if config is None:
            config = self.config
            
        config["last_updated"] = datetime.now().isoformat()
        
        try:
            # Ensure config directory exists
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info(f"Configuration saved to {self.config_file}")
        except Exception as e:
            logger.error(f"Error saving config: {e}")
    
    def get_scraping_config(self) -> Dict[str, Any]:
        """Get scraping timing configuration"""
        return {
            "timing": self.config.get("timing", {}),
            "global_settings": self.config.get("global_settings", {})
        }
    
    def get_all_twitter_accounts(self) -> List[str]:
        """Get all Twitter accounts from global config and dashboards"""
        accounts = set()
        
        # Add global Twitter accounts
        twitter_config = self.config.get("platforms", {}).get("twitter", {})
        if twitter_config.get("enabled", True):
            accounts.update(twitter_config.get("accounts", []))
        
        # Add accounts from dashboards
        for dashboard in self.config.get("dashboards", []):
            for column in dashboard.get("columns", []):
                if column.get("type") == "twitter" and column.get("username"):
                    accounts.add(column["username"])
        
        return list(accounts)
    
    def get_all_facebook_pages(self) -> List[str]:
        """Get all Facebook pages from global config and dashboards"""
        pages = set()
        
        # Add global Facebook pages
        facebook_config = self.config.get("platforms", {}).get("facebook", {})
        if facebook_config.get("enabled", True):
            pages.update(facebook_config.get("pages", []))
        
        # Add pages from dashboards (if any Facebook columns exist)
        for dashboard in self.config.get("dashboards", []):
            for column in dashboard.get("columns", []):
                if column.get("type") == "facebook" and column.get("page_name"):
                    pages.add(column["page_name"])
        
        return list(pages)
    
    def load_dashboards_from_teams_json(self, teams_file: str = "teams.json"):
        """Load dashboard configurations from teams.json"""
        try:
            if os.path.exists(teams_file):
                with open(teams_file, 'r', encoding='utf-8') as f:
                    teams_data = json.load(f)
                
                # Extract dashboards
                dashboards = teams_data.get("dashboards", [])
                self.config["dashboards"] = dashboards
                
                logger.info(f"Loaded {len(dashboards)} dashboards from {teams_file}")
                self.save_config()
                return True
        except Exception as e:
            logger.error(f"Error loading dashboards from {teams_file}: {e}")
        
        return False
    
    def update_timing_config(self, timing_updates: Dict[str, Any]):
        """Update timing configuration"""
        current_timing = self.config.get("timing", {})
        current_timing.update(timing_updates)
        self.config["timing"] = current_timing
        self.save_config()
        logger.info(f"Updated timing configuration: {timing_updates}")
    
    def add_twitter_account(self, username: str):
        """Add a Twitter account to global monitoring"""
        twitter_accounts = self.config.setdefault("platforms", {}).setdefault("twitter", {}).setdefault("accounts", [])
        if username not in twitter_accounts:
            twitter_accounts.append(username)
            self.save_config()
            logger.info(f"Added Twitter account: @{username}")
    
    def add_facebook_page(self, page_name: str):
        """Add a Facebook page to global monitoring"""
        facebook_pages = self.config.setdefault("platforms", {}).setdefault("facebook", {}).setdefault("pages", [])
        if page_name not in facebook_pages:
            facebook_pages.append(page_name)
            self.save_config()
            logger.info(f"Added Facebook page: {page_name}")
    
    def get_platform_enabled(self, platform: str) -> bool:
        """Check if a platform is enabled"""
        return self.config.get("platforms", {}).get(platform, {}).get("enabled", True)
    
    def set_platform_enabled(self, platform: str, enabled: bool):
        """Enable or disable a platform"""
        self.config.setdefault("platforms", {}).setdefault(platform, {})["enabled"] = enabled
        self.save_config()
        logger.info(f"Set {platform} enabled: {enabled}")
    
    def get_scraping_interval(self) -> int:
        """Get scraping interval in minutes"""
        return self.config.get("timing", {}).get("scraping_interval_minutes", 5)
    
    def set_scraping_interval(self, minutes: int):
        """Set scraping interval in minutes"""
        self.config.setdefault("timing", {})["scraping_interval_minutes"] = minutes
        self.save_config()
        logger.info(f"Set scraping interval: {minutes} minutes")
