"""
Database Manager for Social Media Monitoring Tool
Handles SQLite database operations, schema creation, and data management
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import os

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, db_path: str = "social_monitor.db"):
        self.db_path = db_path
        
    def get_connection(self) -> sqlite3.Connection:
        """Get database connection with row factory"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with required tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Posts table - stores all social media posts
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS posts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    platform TEXT NOT NULL,
                    post_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    author TEXT,
                    timestamp DATETIME NOT NULL,
                    url TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(platform, post_id)
                )
            ''')
            
            # Engagement table - tracks engagement metrics over time
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS engagement (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_id INTEGER NOT NULL,
                    likes INTEGER DEFAULT 0,
                    shares INTEGER DEFAULT 0,
                    comments INTEGER DEFAULT 0,
                    retweets INTEGER DEFAULT 0,
                    replies INTEGER DEFAULT 0,
                    total_engagement INTEGER DEFAULT 0,
                    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (post_id) REFERENCES posts (id)
                )
            ''')
            
            # Velocity table - stores calculated velocity scores
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS velocity (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    post_id INTEGER NOT NULL,
                    velocity_score REAL NOT NULL,
                    is_trending BOOLEAN DEFAULT FALSE,
                    calculated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (post_id) REFERENCES posts (id)
                )
            ''')
            
            # Create indexes for better performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_posts_platform ON posts(platform)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_posts_timestamp ON posts(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_engagement_post_id ON engagement(post_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_engagement_recorded_at ON engagement(recorded_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_velocity_trending ON velocity(is_trending)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_velocity_score ON velocity(velocity_score)')
            
            conn.commit()
            logger.info("Database initialized successfully")
    
    def insert_post(self, platform: str, post_id: str, content: str, 
                   author: str, timestamp: datetime, url: str = None) -> Optional[int]:
        """Insert a new post, return the database ID if successful"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO posts 
                    (platform, post_id, content, author, timestamp, url)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (platform, post_id, content, author, timestamp, url))
                
                if cursor.rowcount > 0:
                    # Get the ID of the inserted post
                    cursor.execute('''
                        SELECT id FROM posts WHERE platform = ? AND post_id = ?
                    ''', (platform, post_id))
                    result = cursor.fetchone()
                    return result['id'] if result else None
                else:
                    # Post already exists, get its ID
                    cursor.execute('''
                        SELECT id FROM posts WHERE platform = ? AND post_id = ?
                    ''', (platform, post_id))
                    result = cursor.fetchone()
                    return result['id'] if result else None
                    
        except Exception as e:
            logger.error(f"Error inserting post: {e}")
            return None
    
    def insert_engagement(self, post_db_id: int, likes: int = 0, shares: int = 0, 
                         comments: int = 0, retweets: int = 0, replies: int = 0) -> bool:
        """Insert engagement metrics for a post"""
        try:
            total_engagement = likes + shares + comments + retweets + replies
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO engagement 
                    (post_id, likes, shares, comments, retweets, replies, total_engagement)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (post_db_id, likes, shares, comments, retweets, replies, total_engagement))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error inserting engagement: {e}")
            return False
    
    def get_latest_engagement(self, post_db_id: int) -> Optional[Dict]:
        """Get the most recent engagement data for a post"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM engagement 
                    WHERE post_id = ? 
                    ORDER BY recorded_at DESC 
                    LIMIT 1
                ''', (post_db_id,))
                
                result = cursor.fetchone()
                return dict(result) if result else None
                
        except Exception as e:
            logger.error(f"Error getting latest engagement: {e}")
            return None
    
    def calculate_and_store_velocity(self, post_db_id: int, threshold: float = 0.1) -> Optional[float]:
        """Calculate velocity score and store it"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get the two most recent engagement records
                cursor.execute('''
                    SELECT total_engagement, recorded_at 
                    FROM engagement 
                    WHERE post_id = ? 
                    ORDER BY recorded_at DESC 
                    LIMIT 2
                ''', (post_db_id,))
                
                results = cursor.fetchall()
                
                if len(results) < 2:
                    return None
                
                current = results[0]
                previous = results[1]
                
                # Calculate time difference in seconds
                current_time = datetime.fromisoformat(current['recorded_at'])
                previous_time = datetime.fromisoformat(previous['recorded_at'])
                time_diff = (current_time - previous_time).total_seconds()
                
                if time_diff <= 0:
                    return None
                
                # Calculate velocity
                engagement_diff = current['total_engagement'] - previous['total_engagement']
                velocity_score = engagement_diff / time_diff
                
                # Determine if trending
                is_trending = velocity_score > threshold
                
                # Store velocity
                cursor.execute('''
                    INSERT INTO velocity (post_id, velocity_score, is_trending)
                    VALUES (?, ?, ?)
                ''', (post_db_id, velocity_score, is_trending))
                
                conn.commit()
                return velocity_score
                
        except Exception as e:
            logger.error(f"Error calculating velocity: {e}")
            return None

    def get_trending_posts(self, limit: int = 50, platform: str = None,
                          keyword: str = None) -> List[Dict]:
        """Get trending posts with optional filters"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT
                        p.id, p.platform, p.post_id, p.content, p.author,
                        p.timestamp, p.url,
                        e.likes, e.shares, e.comments, e.retweets, e.replies,
                        e.total_engagement,
                        v.velocity_score, v.is_trending, v.calculated_at
                    FROM posts p
                    JOIN engagement e ON p.id = e.post_id
                    JOIN velocity v ON p.id = v.post_id
                    WHERE v.is_trending = 1
                '''

                params = []

                if platform:
                    query += ' AND p.platform = ?'
                    params.append(platform)

                if keyword:
                    query += ' AND p.content LIKE ?'
                    params.append(f'%{keyword}%')

                query += '''
                    AND e.recorded_at = (
                        SELECT MAX(recorded_at)
                        FROM engagement e2
                        WHERE e2.post_id = p.id
                    )
                    AND v.calculated_at = (
                        SELECT MAX(calculated_at)
                        FROM velocity v2
                        WHERE v2.post_id = p.id
                    )
                    ORDER BY v.velocity_score DESC
                    LIMIT ?
                '''

                params.append(limit)

                cursor.execute(query, params)
                results = cursor.fetchall()

                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error getting trending posts: {e}")
            return []

    def get_all_posts(self, limit: int = 100, platform: str = None,
                     keyword: str = None, sort_by: str = 'timestamp') -> List[Dict]:
        """Get all posts with optional filters and sorting"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                query = '''
                    SELECT
                        p.id, p.platform, p.post_id, p.content, p.author,
                        p.timestamp, p.url,
                        COALESCE(e.likes, 0) as likes,
                        COALESCE(e.shares, 0) as shares,
                        COALESCE(e.comments, 0) as comments,
                        COALESCE(e.retweets, 0) as retweets,
                        COALESCE(e.replies, 0) as replies,
                        COALESCE(e.total_engagement, 0) as total_engagement,
                        COALESCE(v.velocity_score, 0) as velocity_score,
                        COALESCE(v.is_trending, 0) as is_trending
                    FROM posts p
                    LEFT JOIN engagement e ON p.id = e.post_id
                        AND e.recorded_at = (
                            SELECT MAX(recorded_at)
                            FROM engagement e2
                            WHERE e2.post_id = p.id
                        )
                    LEFT JOIN velocity v ON p.id = v.post_id
                        AND v.calculated_at = (
                            SELECT MAX(calculated_at)
                            FROM velocity v2
                            WHERE v2.post_id = p.id
                        )
                    WHERE 1=1
                '''

                params = []

                if platform:
                    query += ' AND p.platform = ?'
                    params.append(platform)

                if keyword:
                    query += ' AND p.content LIKE ?'
                    params.append(f'%{keyword}%')

                # Add sorting
                if sort_by == 'velocity':
                    query += ' ORDER BY velocity_score DESC'
                elif sort_by == 'engagement':
                    query += ' ORDER BY total_engagement DESC'
                else:  # default to timestamp
                    query += ' ORDER BY p.timestamp DESC'

                query += ' LIMIT ?'
                params.append(limit)

                cursor.execute(query, params)
                results = cursor.fetchall()

                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error getting posts: {e}")
            return []

    def cleanup_old_data(self, days: int = 30):
        """Clean up old engagement and velocity data"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # Keep only the latest engagement record per post and records from last N days
                cursor.execute('''
                    DELETE FROM engagement
                    WHERE recorded_at < datetime('now', '-{} days')
                    AND id NOT IN (
                        SELECT MAX(id)
                        FROM engagement
                        GROUP BY post_id
                    )
                '''.format(days))

                # Keep only recent velocity records
                cursor.execute('''
                    DELETE FROM velocity
                    WHERE calculated_at < datetime('now', '-{} days')
                    AND id NOT IN (
                        SELECT MAX(id)
                        FROM velocity
                        GROUP BY post_id
                    )
                '''.format(days))

                conn.commit()
                logger.info(f"Cleaned up data older than {days} days")

        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
