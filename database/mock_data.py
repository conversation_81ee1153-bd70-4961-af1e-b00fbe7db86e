"""
Mock Data Generator for Social Media Monitoring Tool
Generates realistic mock data when scraping fails to ensure dashboard stability
"""

import logging
import random
from datetime import datetime, timedelta
from typing import List, Dict
from database.db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class MockDataGenerator:
    def __init__(self):
        self.db_manager = DatabaseManager()
        
        # Sample content templates
        self.facebook_templates = [
            "🚨 BREAKING: Major development in {topic} - experts say this could change everything",
            "LIVE: {event_type} happening now - follow for updates 📺",
            "ANALYSIS: What the latest {topic} news means for {impact_area}",
            "UPDATE: New information emerges about {topic} situation",
            "EXCLUSIVE: Inside look at {topic} - what you need to know",
            "TRENDING: {topic} discussion reaches new heights as {event_type} unfolds",
            "WATCH: Incredible footage from {event_type} shows {topic} in action",
            "OPINION: Why {topic} matters more than you think - expert weighs in",
            "DEVELOPING: {topic} story continues to evolve - latest details here",
            "VIRAL: This {topic} post is taking the internet by storm 🔥"
        ]
        
        self.twitter_templates = [
            "🚨 BREAKING: {topic} news just in - this is huge",
            "THREAD: Everything you need to know about {topic} 🧵 1/",
            "LIVE: {event_type} happening right now - watching closely 👀",
            "UPDATE: {topic} situation developing - will keep you posted",
            "EXCLUSIVE: Sources tell us {topic} could be game-changing",
            "VIRAL: This {topic} video is everywhere right now 📱",
            "ANALYSIS: {topic} impact on {impact_area} could be significant",
            "WATCH: {event_type} footage shows {topic} in real-time",
            "OPINION: Hot take on {topic} - agree or disagree? 🤔",
            "DEVELOPING: {topic} story getting more interesting by the hour"
        ]
        
        # Topic categories
        self.topics = [
            "artificial intelligence", "climate change", "space exploration", "medical breakthrough",
            "economic policy", "technology innovation", "social media", "cybersecurity",
            "renewable energy", "quantum computing", "biotechnology", "cryptocurrency",
            "global politics", "scientific discovery", "environmental protection", "digital privacy",
            "automation", "gene therapy", "sustainable development", "data science"
        ]
        
        # Event types
        self.event_types = [
            "press conference", "breaking news", "special report", "live coverage",
            "expert panel", "emergency briefing", "exclusive interview", "documentary",
            "research presentation", "policy announcement", "product launch", "summit meeting"
        ]
        
        # Impact areas
        self.impact_areas = [
            "the economy", "global markets", "society", "the future", "healthcare",
            "education", "employment", "international relations", "technology sector",
            "environmental policy", "public health", "scientific research", "innovation"
        ]
        
        # Sample Facebook pages and Twitter accounts
        self.facebook_pages = [
            "TechNews", "ScienceDaily", "GlobalUpdate", "BreakingWorld", "FutureToday",
            "InnovationHub", "WorldReport", "TechTrends", "ScienceNow", "NewsFlash"
        ]
        
        self.twitter_accounts = [
            "TechNewsNow", "ScienceAlert", "BreakingTech", "FutureNews", "InnovateTech",
            "WorldUpdates", "TechTrends24", "ScienceToday", "NewsWire", "TechWatch"
        ]
    
    def generate_facebook_post(self, page_name: str = None) -> Dict:
        """Generate a realistic Facebook post"""
        if not page_name:
            page_name = random.choice(self.facebook_pages)
        
        template = random.choice(self.facebook_templates)
        topic = random.choice(self.topics)
        event_type = random.choice(self.event_types)
        impact_area = random.choice(self.impact_areas)
        
        content = template.format(
            topic=topic,
            event_type=event_type,
            impact_area=impact_area
        )
        
        # Add some randomness to engagement
        base_likes = random.randint(50, 2000)
        base_shares = random.randint(10, 500)
        base_comments = random.randint(5, 300)
        
        # Some posts are more viral
        if random.random() < 0.1:  # 10% chance of viral post
            base_likes *= random.randint(5, 20)
            base_shares *= random.randint(3, 10)
            base_comments *= random.randint(2, 8)
        
        post_time = datetime.now() - timedelta(
            minutes=random.randint(1, 1440)  # Last 24 hours
        )
        
        return {
            'platform': 'facebook',
            'post_id': f"mock_fb_{page_name}_{int(post_time.timestamp())}_{random.randint(1000, 9999)}",
            'content': content,
            'author': page_name,
            'timestamp': post_time,
            'url': f"https://facebook.com/{page_name}/posts/{random.randint(100000000000000000, 999999999999999999)}",
            'likes': base_likes,
            'shares': base_shares,
            'comments': base_comments
        }
    
    def generate_twitter_post(self, username: str = None) -> Dict:
        """Generate a realistic Twitter post"""
        if not username:
            username = random.choice(self.twitter_accounts)
        
        template = random.choice(self.twitter_templates)
        topic = random.choice(self.topics)
        event_type = random.choice(self.event_types)
        impact_area = random.choice(self.impact_areas)
        
        content = template.format(
            topic=topic,
            event_type=event_type,
            impact_area=impact_area
        )
        
        # Twitter has different engagement patterns
        base_likes = random.randint(20, 5000)
        base_retweets = random.randint(5, 1000)
        base_replies = random.randint(2, 500)
        
        # Some tweets go viral
        if random.random() < 0.15:  # 15% chance of viral tweet
            base_likes *= random.randint(10, 50)
            base_retweets *= random.randint(5, 25)
            base_replies *= random.randint(3, 15)
        
        post_time = datetime.now() - timedelta(
            minutes=random.randint(1, 720)  # Last 12 hours (Twitter moves faster)
        )
        
        return {
            'platform': 'twitter',
            'post_id': f"mock_tw_{username}_{int(post_time.timestamp())}_{random.randint(1000, 9999)}",
            'content': content,
            'author': f"@{username}",
            'timestamp': post_time,
            'url': f"https://twitter.com/{username}/status/{random.randint(1000000000000000000, 9999999999999999999)}",
            'likes': base_likes,
            'retweets': base_retweets,
            'replies': base_replies
        }
    
    def generate_trending_posts(self, count: int = 10) -> List[Dict]:
        """Generate a mix of trending posts from both platforms"""
        posts = []
        
        # Generate mix of Facebook and Twitter posts
        for _ in range(count):
            if random.random() < 0.5:  # 50/50 split
                post = self.generate_facebook_post()
            else:
                post = self.generate_twitter_post()
            
            # Make some posts more likely to be trending
            if random.random() < 0.3:  # 30% chance to boost engagement
                if post['platform'] == 'facebook':
                    post['likes'] *= random.randint(2, 5)
                    post['shares'] *= random.randint(2, 4)
                    post['comments'] *= random.randint(2, 3)
                else:  # Twitter
                    post['likes'] *= random.randint(3, 8)
                    post['retweets'] *= random.randint(2, 6)
                    post['replies'] *= random.randint(2, 4)
            
            posts.append(post)
        
        return posts
    
    def populate_database_with_mock_data(self, facebook_posts: int = 20, twitter_posts: int = 30) -> Dict[str, int]:
        """Populate database with mock data"""
        logger.info("Generating mock data for database...")
        
        results = {
            'facebook_saved': 0,
            'twitter_saved': 0,
            'total_saved': 0
        }
        
        try:
            # Generate Facebook posts
            for _ in range(facebook_posts):
                post = self.generate_facebook_post()
                
                # Save to database
                post_db_id = self.db_manager.insert_post(
                    platform=post['platform'],
                    post_id=post['post_id'],
                    content=post['content'],
                    author=post['author'],
                    timestamp=post['timestamp'],
                    url=post['url']
                )
                
                if post_db_id:
                    # Add engagement data
                    success = self.db_manager.insert_engagement(
                        post_db_id=post_db_id,
                        likes=post['likes'],
                        shares=post['shares'],
                        comments=post['comments']
                    )
                    
                    if success:
                        # Add a second engagement record for velocity calculation
                        future_time = post['timestamp'] + timedelta(minutes=random.randint(30, 180))
                        if future_time <= datetime.now():
                            # Simulate engagement growth
                            growth_factor = random.uniform(1.1, 3.0)
                            self.db_manager.insert_engagement(
                                post_db_id=post_db_id,
                                likes=int(post['likes'] * growth_factor),
                                shares=int(post['shares'] * growth_factor),
                                comments=int(post['comments'] * growth_factor)
                            )
                            
                            # Calculate velocity
                            self.db_manager.calculate_and_store_velocity(post_db_id)
                        
                        results['facebook_saved'] += 1
            
            # Generate Twitter posts
            for _ in range(twitter_posts):
                post = self.generate_twitter_post()
                
                # Save to database
                post_db_id = self.db_manager.insert_post(
                    platform=post['platform'],
                    post_id=post['post_id'],
                    content=post['content'],
                    author=post['author'],
                    timestamp=post['timestamp'],
                    url=post['url']
                )
                
                if post_db_id:
                    # Add engagement data
                    success = self.db_manager.insert_engagement(
                        post_db_id=post_db_id,
                        likes=post['likes'],
                        retweets=post['retweets'],
                        replies=post['replies']
                    )
                    
                    if success:
                        # Add a second engagement record for velocity calculation
                        future_time = post['timestamp'] + timedelta(minutes=random.randint(15, 120))
                        if future_time <= datetime.now():
                            # Simulate engagement growth
                            growth_factor = random.uniform(1.2, 4.0)  # Twitter can grow faster
                            self.db_manager.insert_engagement(
                                post_db_id=post_db_id,
                                likes=int(post['likes'] * growth_factor),
                                retweets=int(post['retweets'] * growth_factor),
                                replies=int(post['replies'] * growth_factor)
                            )
                            
                            # Calculate velocity
                            self.db_manager.calculate_and_store_velocity(post_db_id)
                        
                        results['twitter_saved'] += 1
            
            results['total_saved'] = results['facebook_saved'] + results['twitter_saved']
            
            logger.info(f"Mock data generation completed: {results['total_saved']} posts saved")
            
        except Exception as e:
            logger.error(f"Error generating mock data: {e}")
        
        return results
