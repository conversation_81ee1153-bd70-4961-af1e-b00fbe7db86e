"""
Velocity Calculation Engine for Social Media Monitoring Tool
Advanced algorithms for calculating engagement velocity and identifying trending content
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import math
from database.db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class VelocityEngine:
    def __init__(self, db_manager: DatabaseManager = None):
        self.db_manager = db_manager or DatabaseManager()
        
        # Velocity thresholds for different platforms
        self.velocity_thresholds = {
            'facebook': 0.1,  # engagements per second
            'twitter': 0.2,   # engagements per second (Twitter moves faster)
            'default': 0.1
        }
        
        # Weights for different engagement types
        self.engagement_weights = {
            'likes': 1.0,
            'shares': 2.0,      # Shares are more valuable
            'comments': 1.5,    # Comments show deeper engagement
            'retweets': 2.0,    # Retweets are like shares
            'replies': 1.5      # Replies are like comments
        }
    
    def calculate_weighted_engagement(self, engagement_data: Dict) -> float:
        """Calculate weighted engagement score"""
        weighted_score = 0.0
        
        weighted_score += engagement_data.get('likes', 0) * self.engagement_weights['likes']
        weighted_score += engagement_data.get('shares', 0) * self.engagement_weights['shares']
        weighted_score += engagement_data.get('comments', 0) * self.engagement_weights['comments']
        weighted_score += engagement_data.get('retweets', 0) * self.engagement_weights['retweets']
        weighted_score += engagement_data.get('replies', 0) * self.engagement_weights['replies']
        
        return weighted_score
    
    def calculate_simple_velocity(self, current_engagement: int, previous_engagement: int, 
                                 time_diff_seconds: float) -> float:
        """Calculate simple velocity: change in engagement per second"""
        if time_diff_seconds <= 0:
            return 0.0
        
        engagement_change = current_engagement - previous_engagement
        return engagement_change / time_diff_seconds
    
    def calculate_acceleration(self, velocities: List[float]) -> float:
        """Calculate acceleration (change in velocity over time)"""
        if len(velocities) < 2:
            return 0.0
        
        # Simple acceleration: difference between latest and previous velocity
        return velocities[-1] - velocities[-2]
    
    def calculate_momentum_score(self, post_db_id: int, lookback_hours: int = 6) -> Optional[float]:
        """Calculate momentum score based on recent engagement pattern"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get engagement data from the last N hours
                cursor.execute('''
                    SELECT total_engagement, recorded_at 
                    FROM engagement 
                    WHERE post_id = ? 
                    AND recorded_at >= datetime('now', '-{} hours')
                    ORDER BY recorded_at ASC
                '''.format(lookback_hours), (post_db_id,))
                
                results = cursor.fetchall()
                
                if len(results) < 3:  # Need at least 3 data points
                    return None
                
                # Calculate velocities between consecutive points
                velocities = []
                for i in range(1, len(results)):
                    current = results[i]
                    previous = results[i-1]
                    
                    current_time = datetime.fromisoformat(current['recorded_at'])
                    previous_time = datetime.fromisoformat(previous['recorded_at'])
                    time_diff = (current_time - previous_time).total_seconds()
                    
                    if time_diff > 0:
                        velocity = self.calculate_simple_velocity(
                            current['total_engagement'],
                            previous['total_engagement'],
                            time_diff
                        )
                        velocities.append(velocity)
                
                if not velocities:
                    return None
                
                # Calculate momentum as weighted average of recent velocities
                # Give more weight to recent velocities
                weighted_sum = 0.0
                weight_sum = 0.0
                
                for i, velocity in enumerate(velocities):
                    weight = (i + 1) / len(velocities)  # Linear weighting
                    weighted_sum += velocity * weight
                    weight_sum += weight
                
                momentum = weighted_sum / weight_sum if weight_sum > 0 else 0.0
                return momentum
                
        except Exception as e:
            logger.error(f"Error calculating momentum score: {e}")
            return None
    
    def calculate_viral_potential(self, post_db_id: int) -> Optional[float]:
        """Calculate viral potential based on engagement patterns"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get post info and latest engagement
                cursor.execute('''
                    SELECT p.platform, p.timestamp, e.likes, e.shares, e.comments, 
                           e.retweets, e.replies, e.total_engagement
                    FROM posts p
                    JOIN engagement e ON p.id = e.post_id
                    WHERE p.id = ?
                    AND e.recorded_at = (
                        SELECT MAX(recorded_at) 
                        FROM engagement e2 
                        WHERE e2.post_id = p.id
                    )
                ''', (post_db_id,))
                
                result = cursor.fetchone()
                if not result:
                    return None
                
                post_data = dict(result)
                
                # Calculate age of post in hours
                post_time = datetime.fromisoformat(post_data['timestamp'])
                age_hours = (datetime.now() - post_time).total_seconds() / 3600
                
                if age_hours <= 0:
                    return None
                
                # Calculate engagement rate per hour
                engagement_rate = post_data['total_engagement'] / age_hours
                
                # Calculate share/retweet ratio (viral indicator)
                total_shares = (post_data.get('shares', 0) or 0) + (post_data.get('retweets', 0) or 0)
                total_reactions = post_data['total_engagement']
                
                share_ratio = total_shares / total_reactions if total_reactions > 0 else 0
                
                # Calculate viral potential score
                # Combines engagement rate with share ratio
                viral_potential = engagement_rate * (1 + share_ratio * 2)
                
                # Apply platform-specific multipliers
                platform_multiplier = 1.2 if post_data['platform'] == 'twitter' else 1.0
                viral_potential *= platform_multiplier
                
                return viral_potential
                
        except Exception as e:
            logger.error(f"Error calculating viral potential: {e}")
            return None
    
    def update_all_velocities(self, threshold_multiplier: float = 1.0) -> Dict[str, int]:
        """Update velocity scores for all posts with recent engagement"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get posts that have at least 2 engagement records
                cursor.execute('''
                    SELECT DISTINCT p.id, p.platform
                    FROM posts p
                    WHERE (
                        SELECT COUNT(*) 
                        FROM engagement e 
                        WHERE e.post_id = p.id
                    ) >= 2
                    AND p.timestamp >= datetime('now', '-7 days')
                ''')
                
                posts = cursor.fetchall()
                
                results = {
                    'processed': 0,
                    'trending': 0,
                    'errors': 0
                }
                
                for post in posts:
                    try:
                        post_id = post['id']
                        platform = post['platform']
                        
                        # Get threshold for this platform
                        threshold = self.velocity_thresholds.get(platform, self.velocity_thresholds['default'])
                        threshold *= threshold_multiplier
                        
                        # Calculate and store velocity
                        velocity = self.db_manager.calculate_and_store_velocity(post_id, threshold)
                        
                        if velocity is not None:
                            results['processed'] += 1
                            
                            if velocity > threshold:
                                results['trending'] += 1
                                
                                # Calculate additional metrics for trending posts
                                momentum = self.calculate_momentum_score(post_id)
                                viral_potential = self.calculate_viral_potential(post_id)
                                
                                logger.debug(f"Post {post_id}: velocity={velocity:.4f}, momentum={momentum}, viral={viral_potential}")
                        
                    except Exception as e:
                        logger.error(f"Error processing post {post['id']}: {e}")
                        results['errors'] += 1
                
                logger.info(f"Velocity update completed: {results['processed']} processed, {results['trending']} trending, {results['errors']} errors")
                return results
                
        except Exception as e:
            logger.error(f"Error updating velocities: {e}")
            return {'processed': 0, 'trending': 0, 'errors': 1}
    
    def get_trending_analysis(self, limit: int = 20) -> List[Dict]:
        """Get detailed analysis of trending posts"""
        try:
            trending_posts = self.db_manager.get_trending_posts(limit=limit)
            
            for post in trending_posts:
                post_id = post['id']
                
                # Add momentum score
                momentum = self.calculate_momentum_score(post_id)
                post['momentum_score'] = momentum
                
                # Add viral potential
                viral_potential = self.calculate_viral_potential(post_id)
                post['viral_potential'] = viral_potential
                
                # Calculate engagement rate per hour
                post_time = datetime.fromisoformat(post['timestamp'])
                age_hours = (datetime.now() - post_time).total_seconds() / 3600
                post['age_hours'] = age_hours
                post['engagement_per_hour'] = post['total_engagement'] / age_hours if age_hours > 0 else 0
            
            return trending_posts
            
        except Exception as e:
            logger.error(f"Error getting trending analysis: {e}")
            return []
    
    def set_platform_threshold(self, platform: str, threshold: float):
        """Set velocity threshold for a specific platform"""
        self.velocity_thresholds[platform] = threshold
        logger.info(f"Set velocity threshold for {platform}: {threshold}")
    
    def get_platform_threshold(self, platform: str) -> float:
        """Get velocity threshold for a platform"""
        return self.velocity_thresholds.get(platform, self.velocity_thresholds['default'])
