"""
Dashboard Routes for Social Media Monitoring Tool
Flask routes for the web interface
"""

import logging
from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from datetime import datetime, timedelta
from database.db_manager import DatabaseManager
from database.velocity_engine import VelocityEngine
from scheduler.scraper_scheduler import ScraperScheduler

logger = logging.getLogger(__name__)

# Create Blueprint
dashboard_bp = Blueprint('dashboard', __name__)

# Initialize components
db_manager = DatabaseManager()
velocity_engine = VelocityEngine()
scraper_scheduler = ScraperScheduler()

@dashboard_bp.route('/')
def index():
    """Home page - redirect to trending"""
    return redirect(url_for('dashboard.trending'))

@dashboard_bp.route('/trending')
def trending():
    """Main trending page with search and filters"""
    try:
        # Get query parameters
        platform = request.args.get('platform', '')
        keyword = request.args.get('keyword', '')
        sort_by = request.args.get('sort_by', 'velocity')
        limit = int(request.args.get('limit', 50))
        
        # Get trending posts
        if sort_by == 'velocity':
            posts = db_manager.get_trending_posts(
                limit=limit,
                platform=platform if platform else None,
                keyword=keyword if keyword else None
            )
        else:
            posts = db_manager.get_all_posts(
                limit=limit,
                platform=platform if platform else None,
                keyword=keyword if keyword else None,
                sort_by=sort_by
            )
        
        # Format posts for display
        formatted_posts = []
        for post in posts:
            # Calculate time ago
            post_time = datetime.fromisoformat(post['timestamp'])
            time_ago = format_time_ago(post_time)
            
            # Format engagement numbers
            engagement_text = format_engagement(post, post['platform'])
            
            # Determine trending status
            is_trending = post.get('is_trending', False) or post.get('velocity_score', 0) > 0.1
            
            formatted_post = {
                'id': post['id'],
                'platform': post['platform'],
                'content': post['content'][:200] + '...' if len(post['content']) > 200 else post['content'],
                'full_content': post['content'],
                'author': post['author'],
                'time_ago': time_ago,
                'timestamp': post['timestamp'],
                'url': post.get('url', ''),
                'engagement_text': engagement_text,
                'velocity_score': round(post.get('velocity_score', 0), 4),
                'is_trending': is_trending,
                'total_engagement': post.get('total_engagement', 0)
            }
            
            formatted_posts.append(formatted_post)
        
        # Get statistics
        stats = get_dashboard_stats()
        
        return render_template('trending.html', 
                             posts=formatted_posts,
                             stats=stats,
                             current_platform=platform,
                             current_keyword=keyword,
                             current_sort=sort_by,
                             current_limit=limit)
        
    except Exception as e:
        logger.error(f"Error in trending page: {e}")
        return render_template('error.html', error=str(e)), 500

@dashboard_bp.route('/api/posts')
def api_posts():
    """API endpoint for posts data"""
    try:
        platform = request.args.get('platform')
        keyword = request.args.get('keyword')
        sort_by = request.args.get('sort_by', 'timestamp')
        limit = int(request.args.get('limit', 50))
        
        posts = db_manager.get_all_posts(
            limit=limit,
            platform=platform,
            keyword=keyword,
            sort_by=sort_by
        )
        
        return jsonify({
            'success': True,
            'posts': posts,
            'count': len(posts)
        })
        
    except Exception as e:
        logger.error(f"Error in API posts: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/trending')
def api_trending():
    """API endpoint for trending posts"""
    try:
        platform = request.args.get('platform')
        keyword = request.args.get('keyword')
        limit = int(request.args.get('limit', 20))
        
        posts = db_manager.get_trending_posts(
            limit=limit,
            platform=platform,
            keyword=keyword
        )
        
        return jsonify({
            'success': True,
            'trending_posts': posts,
            'count': len(posts)
        })
        
    except Exception as e:
        logger.error(f"Error in API trending: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/stats')
def api_stats():
    """API endpoint for dashboard statistics"""
    try:
        stats = get_dashboard_stats()
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error in API stats: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/scrape/run', methods=['POST'])
def api_run_scraping():
    """API endpoint to manually trigger scraping"""
    try:
        results = scraper_scheduler.run_all_scrapers()
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        logger.error(f"Error running manual scraping: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/settings')
def settings():
    """Settings page for configuration"""
    try:
        # Get current configuration
        facebook_pages = scraper_scheduler.facebook_scraper.get_monitored_pages()
        twitter_accounts = scraper_scheduler.twitter_scraper.get_monitored_accounts()
        scraper_stats = scraper_scheduler.get_statistics()
        
        return render_template('settings.html',
                             facebook_pages=facebook_pages,
                             twitter_accounts=twitter_accounts,
                             scraper_stats=scraper_stats)
        
    except Exception as e:
        logger.error(f"Error in settings page: {e}")
        return render_template('error.html', error=str(e)), 500

def format_time_ago(timestamp):
    """Format timestamp as 'time ago' string"""
    now = datetime.now()
    if isinstance(timestamp, str):
        timestamp = datetime.fromisoformat(timestamp)
    
    diff = now - timestamp
    
    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days != 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    else:
        return "Just now"

def format_engagement(post, platform):
    """Format engagement metrics for display"""
    if platform == 'facebook':
        likes = post.get('likes', 0)
        shares = post.get('shares', 0)
        comments = post.get('comments', 0)
        return f"{likes} likes, {shares} shares, {comments} comments"
    elif platform == 'twitter':
        likes = post.get('likes', 0)
        retweets = post.get('retweets', 0)
        replies = post.get('replies', 0)
        return f"{likes} likes, {retweets} retweets, {replies} replies"
    else:
        total = post.get('total_engagement', 0)
        return f"{total} total engagement"

def get_dashboard_stats():
    """Get dashboard statistics"""
    try:
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # Total posts
            cursor.execute('SELECT COUNT(*) as count FROM posts')
            total_posts = cursor.fetchone()['count']
            
            # Posts by platform
            cursor.execute('''
                SELECT platform, COUNT(*) as count 
                FROM posts 
                GROUP BY platform
            ''')
            platform_counts = {row['platform']: row['count'] for row in cursor.fetchall()}
            
            # Trending posts
            cursor.execute('''
                SELECT COUNT(*) as count 
                FROM velocity 
                WHERE is_trending = 1
                AND calculated_at >= datetime('now', '-1 hour')
            ''')
            trending_count = cursor.fetchone()['count']
            
            # Recent posts (last hour)
            cursor.execute('''
                SELECT COUNT(*) as count 
                FROM posts 
                WHERE created_at >= datetime('now', '-1 hour')
            ''')
            recent_posts = cursor.fetchone()['count']
            
            return {
                'total_posts': total_posts,
                'platform_counts': platform_counts,
                'trending_count': trending_count,
                'recent_posts': recent_posts,
                'last_updated': datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        return {
            'total_posts': 0,
            'platform_counts': {},
            'trending_count': 0,
            'recent_posts': 0,
            'last_updated': datetime.now().isoformat()
        }
