"""
Settings API Routes for Global Configuration Management
Provides REST API endpoints for managing scraping configuration
"""

import logging
from flask import Blueprint, request, jsonify
from config.global_config import GlobalConfigManager

logger = logging.getLogger(__name__)

settings_api_bp = Blueprint('settings_api', __name__)

# Initialize global config manager
config_manager = GlobalConfigManager()

@settings_api_bp.route('/api/settings/config', methods=['GET'])
def get_configuration():
    """Get current global configuration"""
    try:
        return jsonify({
            'success': True,
            'config': config_manager.config
        })
    except Exception as e:
        logger.error(f"Error getting configuration: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_api_bp.route('/api/settings/timing', methods=['GET'])
def get_timing_config():
    """Get timing configuration"""
    try:
        timing_config = config_manager.get_scraping_config()['timing']
        return jsonify({
            'success': True,
            'timing': timing_config
        })
    except Exception as e:
        logger.error(f"Error getting timing config: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_api_bp.route('/api/settings/timing', methods=['POST'])
def update_timing_config():
    """Update timing configuration"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Validate timing values
        valid_keys = [
            'delay_between_accounts',
            'delay_between_platforms', 
            'delay_after_all_platforms_complete',
            'delay_between_posts',
            'random_delay_factor',
            'scraping_interval_minutes'
        ]
        
        timing_updates = {}
        for key, value in data.items():
            if key in valid_keys:
                if isinstance(value, (int, float)) and value >= 0:
                    timing_updates[key] = float(value)
                else:
                    return jsonify({
                        'success': False,
                        'error': f'Invalid value for {key}: must be a non-negative number'
                    }), 400
        
        if not timing_updates:
            return jsonify({
                'success': False,
                'error': 'No valid timing updates provided'
            }), 400
        
        config_manager.update_timing_config(timing_updates)
        
        return jsonify({
            'success': True,
            'message': 'Timing configuration updated',
            'updated': timing_updates
        })
        
    except Exception as e:
        logger.error(f"Error updating timing config: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_api_bp.route('/api/settings/accounts', methods=['GET'])
def get_accounts():
    """Get all monitored accounts"""
    try:
        twitter_accounts = config_manager.get_all_twitter_accounts()
        facebook_pages = config_manager.get_all_facebook_pages()
        
        return jsonify({
            'success': True,
            'accounts': {
                'twitter': twitter_accounts,
                'facebook': facebook_pages
            }
        })
    except Exception as e:
        logger.error(f"Error getting accounts: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_api_bp.route('/api/settings/accounts/twitter', methods=['POST'])
def add_twitter_account():
    """Add a Twitter account to global monitoring"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        
        if not username:
            return jsonify({
                'success': False,
                'error': 'Username is required'
            }), 400
        
        # Remove @ if present
        username = username.lstrip('@')
        
        config_manager.add_twitter_account(username)
        
        return jsonify({
            'success': True,
            'message': f'Added Twitter account: @{username}',
            'username': username
        })
        
    except Exception as e:
        logger.error(f"Error adding Twitter account: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_api_bp.route('/api/settings/accounts/facebook', methods=['POST'])
def add_facebook_page():
    """Add a Facebook page to global monitoring"""
    try:
        data = request.get_json()
        page_name = data.get('page_name', '').strip()
        
        if not page_name:
            return jsonify({
                'success': False,
                'error': 'Page name is required'
            }), 400
        
        config_manager.add_facebook_page(page_name)
        
        return jsonify({
            'success': True,
            'message': f'Added Facebook page: {page_name}',
            'page_name': page_name
        })
        
    except Exception as e:
        logger.error(f"Error adding Facebook page: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_api_bp.route('/api/settings/platforms/<platform>/enabled', methods=['POST'])
def set_platform_enabled(platform):
    """Enable or disable a platform"""
    try:
        if platform not in ['twitter', 'facebook']:
            return jsonify({
                'success': False,
                'error': 'Invalid platform. Must be twitter or facebook'
            }), 400
        
        data = request.get_json()
        enabled = data.get('enabled')
        
        if enabled is None:
            return jsonify({
                'success': False,
                'error': 'enabled field is required'
            }), 400
        
        config_manager.set_platform_enabled(platform, bool(enabled))
        
        return jsonify({
            'success': True,
            'message': f'{platform.title()} {"enabled" if enabled else "disabled"}',
            'platform': platform,
            'enabled': bool(enabled)
        })
        
    except Exception as e:
        logger.error(f"Error setting platform enabled: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_api_bp.route('/api/settings/scraping-interval', methods=['POST'])
def set_scraping_interval():
    """Set scraping interval in minutes"""
    try:
        data = request.get_json()
        minutes = data.get('minutes')
        
        if not isinstance(minutes, (int, float)) or minutes < 1:
            return jsonify({
                'success': False,
                'error': 'Minutes must be a number >= 1'
            }), 400
        
        config_manager.set_scraping_interval(int(minutes))
        
        return jsonify({
            'success': True,
            'message': f'Scraping interval set to {minutes} minutes',
            'interval_minutes': int(minutes)
        })
        
    except Exception as e:
        logger.error(f"Error setting scraping interval: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@settings_api_bp.route('/api/settings/reload-dashboards', methods=['POST'])
def reload_dashboards():
    """Reload dashboard configurations from teams.json"""
    try:
        success = config_manager.load_dashboards_from_teams_json()
        
        if success:
            twitter_accounts = config_manager.get_all_twitter_accounts()
            facebook_pages = config_manager.get_all_facebook_pages()
            
            return jsonify({
                'success': True,
                'message': 'Dashboards reloaded successfully',
                'accounts': {
                    'twitter_count': len(twitter_accounts),
                    'facebook_count': len(facebook_pages)
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to reload dashboards'
            }), 500
            
    except Exception as e:
        logger.error(f"Error reloading dashboards: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
