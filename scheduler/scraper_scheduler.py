"""
Scraper Scheduler for Social Media Monitoring Tool
Manages background scraping jobs with APScheduler and global configuration
"""

import logging
from datetime import datetime
from typing import Dict, Any
import traceback
import time

from scrapers.facebook import FacebookScraper
from scrapers.twitter import TwitterScraper
from database.velocity_engine import VelocityEngine
from database.db_manager import DatabaseManager
from config.global_config import GlobalConfigManager

logger = logging.getLogger(__name__)

class ScraperScheduler:
    def __init__(self):
        # Initialize global configuration
        self.config_manager = GlobalConfigManager()

        # Load dashboards from teams.json
        self.config_manager.load_dashboards_from_teams_json()

        # Initialize scrapers with configuration
        self.facebook_scraper = FacebookScraper(self.config_manager)
        self.twitter_scraper = TwitterScraper(self.config_manager)
        self.velocity_engine = VelocityEngine()
        self.db_manager = DatabaseManager()

        # Get timing configuration
        self.timing_config = self.config_manager.get_scraping_config()['timing']

        # Initialize statistics
        self.stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'last_run': None,
            'last_success': None,
            'last_error': None
        }
        
        # Track scraping statistics
        self.stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'last_run': None,
            'last_success': None,
            'last_error': None
        }
    
    def run_facebook_scraping(self) -> Dict[str, Any]:
        """Run Facebook scraping job"""
        logger.info("Starting Facebook scraping job")
        start_time = datetime.now()
        
        try:
            results = self.facebook_scraper.scrape_all_pages()
            
            duration = (datetime.now() - start_time).total_seconds()
            total_posts = sum(results.values())
            
            logger.info(f"Facebook scraping completed in {duration:.2f}s - {total_posts} posts saved")
            
            return {
                'success': True,
                'platform': 'facebook',
                'duration': duration,
                'posts_saved': total_posts,
                'page_results': results,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            error_msg = f"Facebook scraping failed: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'platform': 'facebook',
                'duration': duration,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    def run_twitter_scraping(self) -> Dict[str, Any]:
        """Run Twitter scraping job"""
        logger.info("Starting Twitter scraping job")
        start_time = datetime.now()
        
        try:
            results = self.twitter_scraper.scrape_all_accounts()
            
            duration = (datetime.now() - start_time).total_seconds()
            total_tweets = sum(results.values())
            
            logger.info(f"Twitter scraping completed in {duration:.2f}s - {total_tweets} tweets saved")
            
            return {
                'success': True,
                'platform': 'twitter',
                'duration': duration,
                'posts_saved': total_tweets,
                'account_results': results,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            error_msg = f"Twitter scraping failed: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'platform': 'twitter',
                'duration': duration,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    def run_velocity_calculations(self) -> Dict[str, Any]:
        """Run velocity calculation job"""
        logger.info("Starting velocity calculations")
        start_time = datetime.now()
        
        try:
            results = self.velocity_engine.update_all_velocities()
            
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Velocity calculations completed in {duration:.2f}s - {results['processed']} posts processed, {results['trending']} trending")
            
            return {
                'success': True,
                'job_type': 'velocity_calculation',
                'duration': duration,
                'processed': results['processed'],
                'trending': results['trending'],
                'errors': results['errors'],
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            error_msg = f"Velocity calculations failed: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'job_type': 'velocity_calculation',
                'duration': duration,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    def run_database_cleanup(self) -> Dict[str, Any]:
        """Run database cleanup job"""
        logger.info("Starting database cleanup")
        start_time = datetime.now()
        
        try:
            self.db_manager.cleanup_old_data(days=7)  # Keep 7 days of data
            
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"Database cleanup completed in {duration:.2f}s")
            
            return {
                'success': True,
                'job_type': 'database_cleanup',
                'duration': duration,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            error_msg = f"Database cleanup failed: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'job_type': 'database_cleanup',
                'duration': duration,
                'error': error_msg,
                'timestamp': datetime.now().isoformat()
            }
    
    def run_all_scrapers(self) -> Dict[str, Any]:
        """Run all scraping jobs in sequence with global timing configuration"""
        logger.info("=" * 50)
        logger.info("Starting scheduled scraping run with global configuration")
        logger.info("=" * 50)

        # Log configuration summary
        twitter_accounts = self.config_manager.get_all_twitter_accounts()
        facebook_pages = self.config_manager.get_all_facebook_pages()

        logger.info(f"Global scraping configuration:")
        logger.info(f"  Twitter accounts: {len(twitter_accounts)} ({', '.join(twitter_accounts[:3])}{'...' if len(twitter_accounts) > 3 else ''})")
        logger.info(f"  Facebook pages: {len(facebook_pages)} ({', '.join(facebook_pages[:3])}{'...' if len(facebook_pages) > 3 else ''})")
        logger.info(f"  Timing: {self.timing_config['delay_between_platforms']}s between platforms, "
                   f"{self.timing_config['delay_after_all_platforms_complete']}s after completion")

        overall_start = datetime.now()
        self.stats['total_runs'] += 1
        self.stats['last_run'] = overall_start.isoformat()

        results = {
            'run_id': int(overall_start.timestamp()),
            'start_time': overall_start.isoformat(),
            'jobs': [],
            'configuration': {
                'twitter_accounts_count': len(twitter_accounts),
                'facebook_pages_count': len(facebook_pages),
                'timing_config': self.timing_config
            }
        }

        success_count = 0
        total_jobs = 0

        try:
            # 1. Run Facebook scraping if enabled
            if self.config_manager.get_platform_enabled('facebook'):
                total_jobs += 1
                fb_result = self.run_facebook_scraping()
                results['jobs'].append(fb_result)
                if fb_result['success']:
                    success_count += 1

                # Delay between platforms
                if self.config_manager.get_platform_enabled('twitter'):
                    delay = self.timing_config['delay_between_platforms']
                    logger.info(f"Waiting {delay}s between Facebook and Twitter platforms...")
                    time.sleep(delay)
            else:
                logger.info("Facebook scraping disabled in configuration")

            # 2. Run Twitter scraping if enabled
            if self.config_manager.get_platform_enabled('twitter'):
                total_jobs += 1
                twitter_result = self.run_twitter_scraping()
                results['jobs'].append(twitter_result)
                if twitter_result['success']:
                    success_count += 1
            else:
                logger.info("Twitter scraping disabled in configuration")

            # 3. Run velocity calculations
            total_jobs += 1
            velocity_result = self.run_velocity_calculations()
            results['jobs'].append(velocity_result)
            if velocity_result['success']:
                success_count += 1

            # 4. Run database cleanup (less frequently)
            if self.stats['total_runs'] % 12 == 0:  # Every 12 runs (1 hour if running every 5 min)
                total_jobs += 1
                cleanup_result = self.run_database_cleanup()
                results['jobs'].append(cleanup_result)
                if cleanup_result['success']:
                    success_count += 1

            # Calculate overall results
            overall_duration = (datetime.now() - overall_start).total_seconds()
            overall_success = success_count == total_jobs
            total_posts_saved = sum(job.get('posts_saved', 0) for job in results['jobs'])

            results.update({
                'end_time': datetime.now().isoformat(),
                'duration': overall_duration,
                'success': overall_success,
                'jobs_completed': success_count,
                'jobs_total': total_jobs,
                'total_posts_saved': total_posts_saved
            })

            # Final delay after all platforms complete
            if total_posts_saved > 0:
                delay = self.timing_config['delay_after_all_platforms_complete']
                logger.info(f"All platforms complete. Final rest period: {delay}s")
                time.sleep(delay)
            
            # Update statistics
            if overall_success:
                self.stats['successful_runs'] += 1
                self.stats['last_success'] = datetime.now().isoformat()
            else:
                self.stats['failed_runs'] += 1
                self.stats['last_error'] = datetime.now().isoformat()
            
            logger.info("=" * 50)
            logger.info(f"Scraping run completed in {overall_duration:.2f}s")
            logger.info(f"Jobs: {success_count}/{total_jobs} successful")
            logger.info(f"Total posts saved: {results['total_posts_saved']}")
            logger.info("=" * 50)
            
        except Exception as e:
            error_msg = f"Critical error in scraping run: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            self.stats['failed_runs'] += 1
            self.stats['last_error'] = datetime.now().isoformat()
            
            results.update({
                'end_time': datetime.now().isoformat(),
                'duration': (datetime.now() - overall_start).total_seconds(),
                'success': False,
                'error': error_msg
            })
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get scraping statistics"""
        return self.stats.copy()
    
    def reset_statistics(self):
        """Reset scraping statistics"""
        self.stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'failed_runs': 0,
            'last_run': None,
            'last_success': None,
            'last_error': None
        }
        logger.info("Scraping statistics reset")
