"""
Facebook Scraper for Social Media Monitoring Tool
Uses facebook-scraper library to collect public posts from Facebook pages
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import random
import time

try:
    from facebook_scraper import get_posts
except ImportError:
    get_posts = None

from database.db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class FacebookScraper:
    def __init__(self, config_manager=None):
        self.db_manager = DatabaseManager()
        self.platform = "facebook"
        self.config_manager = config_manager

        # Load configuration or use defaults
        if config_manager:
            self.config = config_manager.get_scraping_config()
            self.pages_to_monitor = config_manager.get_all_facebook_pages()
        else:
            # Default pages to monitor (fallback)
            self.pages_to_monitor = [
                "BBC",
                "CNN",
                "Reuters",
                "TheNewYorkTimes",
                "washingtonpost"
            ]
            # Default timing configuration
            self.config = {
                'timing': {
                    'delay_between_accounts': 2,
                    'delay_after_platform_complete': 10,
                    'delay_between_posts': 1,
                    'random_delay_factor': 0.5
                }
            }
    
    def generate_mock_post(self, page: str) -> Dict:
        """Generate mock Facebook post data when scraping fails"""
        mock_content = [
            f"Breaking: Major development in {random.choice(['technology', 'politics', 'science', 'business'])}",
            f"LIVE: {random.choice(['Press conference', 'Breaking news', 'Special report'])} happening now",
            f"Analysis: What this means for {random.choice(['the economy', 'global markets', 'society', 'the future'])}",
            f"Update: Latest information on {random.choice(['climate change', 'space exploration', 'medical breakthrough', 'AI development'])}",
            f"Opinion: Why {random.choice(['this matters', 'we should care', 'this changes everything', 'experts are concerned'])}"
        ]
        
        now = datetime.now()
        
        return {
            'post_id': f"mock_{page}_{int(now.timestamp())}_{random.randint(1000, 9999)}",
            'text': random.choice(mock_content),
            'time': now - timedelta(minutes=random.randint(1, 60)),
            'likes': random.randint(10, 1000),
            'comments': random.randint(5, 200),
            'shares': random.randint(2, 100),
            'post_url': f"https://facebook.com/{page}/posts/mock_{random.randint(100000, 999999)}"
        }
    
    def scrape_page(self, page_name: str, max_posts: int = 10) -> List[Dict]:
        """Scrape posts from a Facebook page"""
        posts_data = []
        
        try:
            if get_posts is None:
                logger.warning("facebook-scraper not available, using mock data")
                # Generate mock posts
                for _ in range(max_posts):
                    posts_data.append(self.generate_mock_post(page_name))
                return posts_data
            
            logger.info(f"Scraping Facebook page: {page_name}")
            
            # Get posts from the page
            posts = get_posts(
                page_name, 
                pages=1,  # Only get recent posts
                extra_info=True,
                timeout=30
            )
            
            count = 0
            for post in posts:
                if count >= max_posts:
                    break
                
                # Extract post data
                post_data = {
                    'post_id': post.get('post_id', f"fb_{page_name}_{int(datetime.now().timestamp())}_{count}"),
                    'text': post.get('text', ''),
                    'time': post.get('time', datetime.now()),
                    'likes': post.get('likes', 0) or 0,
                    'comments': post.get('comments', 0) or 0,
                    'shares': post.get('shares', 0) or 0,
                    'post_url': post.get('post_url', '')
                }
                
                # Skip posts without content
                if not post_data['text'] or len(post_data['text'].strip()) < 10:
                    continue
                
                posts_data.append(post_data)
                count += 1
                
                # Add small delay to be respectful
                time.sleep(1)
            
            logger.info(f"Successfully scraped {len(posts_data)} posts from {page_name}")
            
        except Exception as e:
            logger.error(f"Error scraping Facebook page {page_name}: {e}")
            # Generate mock data as fallback
            logger.info(f"Generating mock data for {page_name}")
            for _ in range(min(max_posts, 3)):  # Generate fewer mock posts
                posts_data.append(self.generate_mock_post(page_name))
        
        return posts_data
    
    def save_posts_to_db(self, posts_data: List[Dict], page_name: str) -> int:
        """Save scraped posts to database"""
        saved_count = 0
        
        for post_data in posts_data:
            try:
                # Insert post
                post_db_id = self.db_manager.insert_post(
                    platform=self.platform,
                    post_id=post_data['post_id'],
                    content=post_data['text'],
                    author=page_name,
                    timestamp=post_data['time'],
                    url=post_data.get('post_url', '')
                )
                
                if post_db_id:
                    # Insert engagement data
                    success = self.db_manager.insert_engagement(
                        post_db_id=post_db_id,
                        likes=post_data.get('likes', 0),
                        shares=post_data.get('shares', 0),
                        comments=post_data.get('comments', 0)
                    )
                    
                    if success:
                        # Calculate velocity if we have previous data
                        velocity = self.db_manager.calculate_and_store_velocity(post_db_id)
                        if velocity is not None:
                            logger.debug(f"Calculated velocity {velocity} for post {post_data['post_id']}")
                        
                        saved_count += 1
                
            except Exception as e:
                logger.error(f"Error saving post {post_data.get('post_id', 'unknown')}: {e}")
        
        logger.info(f"Saved {saved_count} posts from {page_name} to database")
        return saved_count
    
    def scrape_all_pages(self) -> Dict[str, int]:
        """Scrape all configured Facebook pages with intelligent timing"""
        results = {}
        total_pages = len(self.pages_to_monitor)

        logger.info(f"Starting Facebook scraping for {total_pages} pages")
        logger.info(f"Timing config: {self.config['timing']['delay_between_accounts']}s between pages, "
                   f"{self.config['timing']['delay_after_platform_complete']}s after completion")

        for i, page in enumerate(self.pages_to_monitor, 1):
            try:
                logger.info(f"[{i}/{total_pages}] Scraping {page}...")

                # Scrape posts
                posts_data = self.scrape_page(page, max_posts=5)

                # Save to database
                saved_count = self.save_posts_to_db(posts_data, page)
                results[page] = saved_count

                logger.info(f"[{i}/{total_pages}] {page}: {saved_count} posts saved")

                # Add intelligent delay between pages (except for last page)
                if i < total_pages:
                    delay = self._calculate_delay_between_pages()
                    logger.info(f"Waiting {delay:.1f}s before next page...")
                    time.sleep(delay)

            except Exception as e:
                logger.error(f"Error processing page {page}: {e}")
                results[page] = 0

        total_saved = sum(results.values())
        logger.info(f"Facebook scraping completed. Total posts saved: {total_saved}")

        # Add delay after completing all Facebook pages
        if total_saved > 0:
            delay = self.config['timing']['delay_after_platform_complete']
            logger.info(f"Facebook platform complete. Waiting {delay}s before next platform...")
            time.sleep(delay)

        return results

    def _calculate_delay_between_pages(self) -> float:
        """Calculate intelligent delay between page scraping"""
        base_delay = self.config['timing']['delay_between_accounts']
        random_factor = self.config['timing']['random_delay_factor']

        # Add random variation to avoid predictable patterns
        variation = random.uniform(-random_factor, random_factor)
        delay = base_delay + (base_delay * variation)

        # Ensure minimum delay of 1 second
        return max(1.0, delay)

    def _delay_between_posts(self):
        """Add small delay between processing individual posts"""
        delay = self.config['timing']['delay_between_posts']
        if delay > 0:
            time.sleep(delay)
    
    def add_page_to_monitor(self, page_name: str):
        """Add a new page to the monitoring list"""
        if page_name not in self.pages_to_monitor:
            self.pages_to_monitor.append(page_name)
            logger.info(f"Added {page_name} to Facebook monitoring list")
    
    def remove_page_from_monitor(self, page_name: str):
        """Remove a page from the monitoring list"""
        if page_name in self.pages_to_monitor:
            self.pages_to_monitor.remove(page_name)
            logger.info(f"Removed {page_name} from Facebook monitoring list")
    
    def get_monitored_pages(self) -> List[str]:
        """Get list of currently monitored pages"""
        return self.pages_to_monitor.copy()
