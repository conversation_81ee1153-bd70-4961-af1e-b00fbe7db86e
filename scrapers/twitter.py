"""
Twitter Scraper for Social Media Monitoring Tool
Uses snscrape library to collect public tweets without using Twitter API
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import random
import time
import subprocess
import json
import os

try:
    import snscrape.modules.twitter as sntwitter
except ImportError:
    sntwitter = None

from database.db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class TwitterScraper:
    def __init__(self, config_manager=None):
        self.db_manager = DatabaseManager()
        self.platform = "twitter"
        self.config_manager = config_manager

        # Load configuration or use defaults
        if config_manager:
            self.config = config_manager.get_scraping_config()
            self.accounts_to_monitor = config_manager.get_all_twitter_accounts()
        else:
            # Default accounts to monitor (fallback)
            self.accounts_to_monitor = [
                "BBCBreaking",
                "CNN",
                "Reuters",
                "nytimes",
                "washingtonpost"
            ]
            # Default timing configuration
            self.config = {
                'timing': {
                    'delay_between_accounts': 3,
                    'delay_after_platform_complete': 10,
                    'delay_between_posts': 1,
                    'random_delay_factor': 0.5
                }
            }
    
    def generate_mock_tweet(self, username: str) -> Dict:
        """Generate mock Twitter data when scraping fails"""
        mock_content = [
            f"🚨 BREAKING: Major development in {random.choice(['tech', 'politics', 'science', 'business'])} sector",
            f"LIVE: {random.choice(['Press conference', 'Breaking news', 'Special report'])} happening now 📺",
            f"THREAD: What this means for {random.choice(['the economy', 'global markets', 'society', 'the future'])} 🧵",
            f"UPDATE: Latest on {random.choice(['climate change', 'space exploration', 'medical breakthrough', 'AI development'])} 🔬",
            f"OPINION: Why {random.choice(['this matters', 'we should care', 'this changes everything', 'experts are concerned'])} 💭"
        ]
        
        now = datetime.now()
        
        return {
            'id': f"mock_{username}_{int(now.timestamp())}_{random.randint(1000, 9999)}",
            'content': random.choice(mock_content),
            'date': now - timedelta(minutes=random.randint(1, 60)),
            'likeCount': random.randint(10, 2000),
            'retweetCount': random.randint(5, 500),
            'replyCount': random.randint(2, 200),
            'url': f"https://twitter.com/{username}/status/mock_{random.randint(100000000000000000, 999999999999999999)}"
        }
    
    def scrape_user_tweets(self, username: str, max_tweets: int = 10) -> List[Dict]:
        """Scrape tweets from a Twitter user"""
        tweets_data = []
        
        try:
            if sntwitter is None:
                logger.warning("snscrape not available, using mock data")
                # Generate mock tweets
                for _ in range(max_tweets):
                    tweets_data.append(self.generate_mock_tweet(username))
                return tweets_data
            
            logger.info(f"Scraping Twitter user: {username}")
            
            # Create scraper for user tweets
            query = f"from:{username}"
            tweets = sntwitter.TwitterSearchScraper(query).get_items()
            
            count = 0
            for tweet in tweets:
                if count >= max_tweets:
                    break
                
                # Skip retweets and replies for cleaner data
                if hasattr(tweet, 'retweetedTweet') and tweet.retweetedTweet:
                    continue
                if hasattr(tweet, 'inReplyToTweetId') and tweet.inReplyToTweetId:
                    continue
                
                # Extract tweet data
                tweet_data = {
                    'id': str(tweet.id),
                    'content': tweet.content or tweet.rawContent or '',
                    'date': tweet.date,
                    'likeCount': getattr(tweet, 'likeCount', 0) or 0,
                    'retweetCount': getattr(tweet, 'retweetCount', 0) or 0,
                    'replyCount': getattr(tweet, 'replyCount', 0) or 0,
                    'url': tweet.url or f"https://twitter.com/{username}/status/{tweet.id}"
                }
                
                # Skip tweets without content
                if not tweet_data['content'] or len(tweet_data['content'].strip()) < 10:
                    continue
                
                tweets_data.append(tweet_data)
                count += 1
                
                # Add small delay to be respectful
                time.sleep(0.5)
            
            logger.info(f"Successfully scraped {len(tweets_data)} tweets from @{username}")
            
        except Exception as e:
            logger.error(f"Error scraping Twitter user @{username}: {e}")
            # Generate mock data as fallback
            logger.info(f"Generating mock data for @{username}")
            for _ in range(min(max_tweets, 3)):  # Generate fewer mock tweets
                tweets_data.append(self.generate_mock_tweet(username))
        
        return tweets_data
    
    def save_tweets_to_db(self, tweets_data: List[Dict], username: str) -> int:
        """Save scraped tweets to database"""
        saved_count = 0
        
        for tweet_data in tweets_data:
            try:
                # Insert tweet
                post_db_id = self.db_manager.insert_post(
                    platform=self.platform,
                    post_id=tweet_data['id'],
                    content=tweet_data['content'],
                    author=f"@{username}",
                    timestamp=tweet_data['date'],
                    url=tweet_data.get('url', '')
                )
                
                if post_db_id:
                    # Insert engagement data
                    success = self.db_manager.insert_engagement(
                        post_db_id=post_db_id,
                        likes=tweet_data.get('likeCount', 0),
                        retweets=tweet_data.get('retweetCount', 0),
                        replies=tweet_data.get('replyCount', 0)
                    )
                    
                    if success:
                        # Calculate velocity if we have previous data
                        velocity = self.db_manager.calculate_and_store_velocity(post_db_id)
                        if velocity is not None:
                            logger.debug(f"Calculated velocity {velocity} for tweet {tweet_data['id']}")
                        
                        saved_count += 1
                
            except Exception as e:
                logger.error(f"Error saving tweet {tweet_data.get('id', 'unknown')}: {e}")
        
        logger.info(f"Saved {saved_count} tweets from @{username} to database")
        return saved_count
    
    def scrape_trending_topics(self, max_tweets: int = 20) -> List[Dict]:
        """Scrape tweets from trending topics or hashtags"""
        tweets_data = []
        
        trending_queries = [
            "#breaking",
            "#news", 
            "#trending",
            "#viral",
            "#update"
        ]
        
        try:
            if sntwitter is None:
                logger.warning("snscrape not available for trending topics, using mock data")
                # Generate mock trending tweets
                for query in trending_queries[:2]:  # Limit mock data
                    for _ in range(2):
                        mock_tweet = self.generate_mock_tweet("trending")
                        mock_tweet['content'] = f"{query} {mock_tweet['content']}"
                        tweets_data.append(mock_tweet)
                return tweets_data
            
            logger.info("Scraping trending topics")
            
            for query in trending_queries:
                try:
                    tweets = sntwitter.TwitterSearchScraper(f"{query} -filter:retweets").get_items()
                    
                    count = 0
                    for tweet in tweets:
                        if count >= max_tweets // len(trending_queries):
                            break
                        
                        # Extract tweet data
                        tweet_data = {
                            'id': str(tweet.id),
                            'content': tweet.content or tweet.rawContent or '',
                            'date': tweet.date,
                            'likeCount': getattr(tweet, 'likeCount', 0) or 0,
                            'retweetCount': getattr(tweet, 'retweetCount', 0) or 0,
                            'replyCount': getattr(tweet, 'replyCount', 0) or 0,
                            'url': tweet.url or f"https://twitter.com/status/{tweet.id}"
                        }
                        
                        # Skip tweets without content
                        if not tweet_data['content'] or len(tweet_data['content'].strip()) < 10:
                            continue
                        
                        tweets_data.append(tweet_data)
                        count += 1
                        
                        time.sleep(0.5)
                    
                    # Delay between different queries
                    time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"Error scraping trending topic {query}: {e}")
                    continue
            
            logger.info(f"Successfully scraped {len(tweets_data)} trending tweets")
            
        except Exception as e:
            logger.error(f"Error scraping trending topics: {e}")
            # Generate some mock trending data
            for _ in range(5):
                mock_tweet = self.generate_mock_tweet("trending")
                mock_tweet['content'] = f"#trending {mock_tweet['content']}"
                tweets_data.append(mock_tweet)
        
        return tweets_data
    
    def scrape_all_accounts(self) -> Dict[str, int]:
        """Scrape all configured Twitter accounts with intelligent timing"""
        results = {}
        total_accounts = len(self.accounts_to_monitor)

        logger.info(f"Starting Twitter scraping for {total_accounts} accounts")
        logger.info(f"Timing config: {self.config['timing']['delay_between_accounts']}s between accounts, "
                   f"{self.config['timing']['delay_after_platform_complete']}s after completion")

        for i, username in enumerate(self.accounts_to_monitor, 1):
            try:
                logger.info(f"[{i}/{total_accounts}] Scraping @{username}...")

                # Scrape tweets
                tweets_data = self.scrape_user_tweets(username, max_tweets=5)

                # Save to database
                saved_count = self.save_tweets_to_db(tweets_data, username)
                results[username] = saved_count

                logger.info(f"[{i}/{total_accounts}] @{username}: {saved_count} tweets saved")

                # Add intelligent delay between accounts (except for last account)
                if i < total_accounts:
                    delay = self._calculate_delay_between_accounts()
                    logger.info(f"Waiting {delay:.1f}s before next account...")
                    time.sleep(delay)

            except Exception as e:
                logger.error(f"Error processing account @{username}: {e}")
                results[username] = 0
        
        # Also scrape some trending topics
        try:
            trending_tweets = self.scrape_trending_topics(max_tweets=10)
            trending_saved = 0
            
            for tweet_data in trending_tweets:
                try:
                    post_db_id = self.db_manager.insert_post(
                        platform=self.platform,
                        post_id=tweet_data['id'],
                        content=tweet_data['content'],
                        author="trending",
                        timestamp=tweet_data['date'],
                        url=tweet_data.get('url', '')
                    )
                    
                    if post_db_id:
                        success = self.db_manager.insert_engagement(
                            post_db_id=post_db_id,
                            likes=tweet_data.get('likeCount', 0),
                            retweets=tweet_data.get('retweetCount', 0),
                            replies=tweet_data.get('replyCount', 0)
                        )
                        
                        if success:
                            self.db_manager.calculate_and_store_velocity(post_db_id)
                            trending_saved += 1
                            
                except Exception as e:
                    logger.error(f"Error saving trending tweet: {e}")
            
            results['trending_topics'] = trending_saved
            
        except Exception as e:
            logger.error(f"Error processing trending topics: {e}")
            results['trending_topics'] = 0

        total_saved = sum(results.values())
        logger.info(f"Twitter scraping completed. Total tweets saved: {total_saved}")

        # Add delay after completing all Twitter accounts
        if total_saved > 0:
            delay = self.config['timing']['delay_after_platform_complete']
            logger.info(f"Twitter platform complete. Waiting {delay}s before next platform...")
            time.sleep(delay)

        return results

    def _calculate_delay_between_accounts(self) -> float:
        """Calculate intelligent delay between account scraping"""
        base_delay = self.config['timing']['delay_between_accounts']
        random_factor = self.config['timing']['random_delay_factor']

        # Add random variation to avoid predictable patterns
        variation = random.uniform(-random_factor, random_factor)
        delay = base_delay + (base_delay * variation)

        # Ensure minimum delay of 1 second
        return max(1.0, delay)

    def _delay_between_posts(self):
        """Add small delay between processing individual posts"""
        delay = self.config['timing']['delay_between_posts']
        if delay > 0:
            time.sleep(delay)

    def add_account_to_monitor(self, username: str):
        """Add a new account to the monitoring list"""
        if username not in self.accounts_to_monitor:
            self.accounts_to_monitor.append(username)
            logger.info(f"Added @{username} to Twitter monitoring list")

    def remove_account_from_monitor(self, username: str):
        """Remove an account from the monitoring list"""
        if username in self.accounts_to_monitor:
            self.accounts_to_monitor.remove(username)
            logger.info(f"Removed @{username} from Twitter monitoring list")

    def get_monitored_accounts(self) -> List[str]:
        """Get list of currently monitored accounts"""
        return self.accounts_to_monitor.copy()
