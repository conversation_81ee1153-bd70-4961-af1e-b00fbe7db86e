<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Social Media Monitor{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .trending-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 0.75rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .platform-badge {
            font-size: 0.75rem;
        }
        
        .platform-facebook {
            background-color: #1877f2;
            color: white;
        }
        
        .platform-twitter {
            background-color: #1da1f2;
            color: white;
        }
        
        .velocity-score {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #666;
        }
        
        .post-content {
            line-height: 1.4;
        }
        
        .engagement-stats {
            font-size: 0.85rem;
            color: #666;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .navbar-dark {
            background-color: #1a1a1a !important;
            border-bottom: 1px solid #333;
        }

        .nav-link {
            font-weight: 500;
            padding: 8px 16px !important;
            margin: 0 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-link.active {
            background-color: #007bff;
            color: white !important;
        }

        .settings-btn {
            background-color: #17a2b8 !important;
            color: white !important;
            border-radius: 4px !important;
            padding: 8px 16px !important;
            margin-left: 8px !important;
            text-decoration: none !important;
        }

        .settings-btn:hover {
            background-color: #138496 !important;
            color: white !important;
        }

        .stats-card {
            border-left: 4px solid #007bff;
        }
        
        .search-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .post-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .time-ago {
            font-size: 0.8rem;
            color: #999;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 20px 0;
            margin-top: 40px;
            border-top: 1px solid #dee2e6;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('dashboard.trending') }}">
                <span class="me-2" style="font-size: 1.5rem;">📊</span>
                <strong>Feeder</strong>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard.trending' %}active{% endif %}"
                           href="{{ url_for('dashboard.trending') }}">
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link settings-btn {% if request.endpoint == 'dashboard.settings' %}active{% endif %}"
                           href="{{ url_for('dashboard.settings') }}">
                            Settings
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text small">
                            <i class="fas fa-clock me-1"></i>
                            Last updated: <span id="last-updated">{{ stats.last_updated if stats else 'Never' }}</span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Social Media Monitor - Real-time tracking without APIs
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <small class="text-muted">
                            Monitoring Facebook & Twitter • Built with Python & Flask
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Auto-refresh functionality -->
    <script>
        // Auto-refresh page every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000); // 5 minutes
        
        // Update last updated time
        function updateLastUpdated() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const element = document.getElementById('last-updated');
            if (element) {
                element.textContent = timeString;
            }
        }
        
        // Update every minute
        setInterval(updateLastUpdated, 60000);
        
        // Manual refresh button functionality
        function manualRefresh() {
            location.reload();
        }
        
        // Trigger manual scraping
        function triggerScraping() {
            fetch('/api/scrape/run', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Scraping triggered successfully!');
                    setTimeout(() => location.reload(), 3000);
                } else {
                    alert('Error triggering scraping: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error);
            });
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
