{% extends "base.html" %}

{% block title %}Settings - Social Media Monitor{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <h2>
            <i class="fas fa-cog me-2"></i>
            Settings & Configuration
        </h2>
        
        <!-- Facebook Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fab fa-facebook text-primary me-2"></i>
                    Facebook Pages
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Currently monitoring these Facebook pages:</p>
                <div class="row">
                    {% for page in facebook_pages %}
                    <div class="col-md-6 mb-2">
                        <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                            <span>{{ page }}</span>
                            <button class="btn btn-sm btn-outline-danger" onclick="removePage('facebook', '{{ page }}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <hr>
                
                <form onsubmit="addPage('facebook'); return false;">
                    <div class="input-group">
                        <input type="text" class="form-control" id="new-facebook-page" 
                               placeholder="Enter Facebook page name (e.g., BBC, CNN)">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-plus me-1"></i>
                            Add Page
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Twitter Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fab fa-twitter text-info me-2"></i>
                    Twitter Accounts
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Currently monitoring these Twitter accounts:</p>
                <div class="row">
                    {% for account in twitter_accounts %}
                    <div class="col-md-6 mb-2">
                        <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                            <span>@{{ account }}</span>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeAccount('twitter', '{{ account }}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <hr>
                
                <form onsubmit="addAccount('twitter'); return false;">
                    <div class="input-group">
                        <span class="input-group-text">@</span>
                        <input type="text" class="form-control" id="new-twitter-account" 
                               placeholder="Enter Twitter username (e.g., BBCBreaking, CNN)">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-plus me-1"></i>
                            Add Account
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Global Scraping Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Global Scraping Configuration
                </h5>
            </div>
            <div class="card-body">
                <!-- Timing Configuration -->
                <h6 class="mb-3"><i class="fas fa-clock me-2"></i>Timing Settings</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="delayBetweenAccounts" class="form-label">Delay Between Accounts (seconds)</label>
                        <input type="number" class="form-control" id="delayBetweenAccounts" min="1" max="60" step="0.5" value="3">
                        <small class="form-text text-muted">Time to wait between scraping different accounts</small>
                    </div>
                    <div class="col-md-6">
                        <label for="delayBetweenPlatforms" class="form-label">Delay Between Platforms (seconds)</label>
                        <input type="number" class="form-control" id="delayBetweenPlatforms" min="5" max="300" step="1" value="15">
                        <small class="form-text text-muted">Time to wait between Facebook and Twitter scraping</small>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="delayAfterComplete" class="form-label">Final Rest Period (seconds)</label>
                        <input type="number" class="form-control" id="delayAfterComplete" min="10" max="600" step="5" value="30">
                        <small class="form-text text-muted">Time to rest after completing all platforms</small>
                    </div>
                    <div class="col-md-6">
                        <label for="scrapingInterval" class="form-label">Scraping Interval (minutes)</label>
                        <input type="number" class="form-control" id="scrapingInterval" min="1" max="60" step="1" value="5">
                        <small class="form-text text-muted">How often to run the complete scraping cycle</small>
                    </div>
                </div>

                <button onclick="updateTimingConfig()" class="btn btn-primary me-2">
                    <i class="fas fa-save me-1"></i>
                    Save Timing Settings
                </button>

                <hr class="my-4">

                <!-- Platform Controls -->
                <h6 class="mb-3"><i class="fas fa-toggle-on me-2"></i>Platform Controls</h6>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="twitterEnabled" checked>
                            <label class="form-check-label" for="twitterEnabled">
                                <i class="fab fa-twitter me-1"></i>Twitter Scraping Enabled
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="facebookEnabled" checked>
                            <label class="form-check-label" for="facebookEnabled">
                                <i class="fab fa-facebook me-1"></i>Facebook Scraping Enabled
                            </label>
                        </div>
                    </div>
                </div>

                <hr class="my-4">

                <!-- Manual Controls -->
                <h6 class="mb-3"><i class="fas fa-robot me-2"></i>Manual Controls</h6>
                <div class="row">
                    <div class="col-md-4">
                        <button onclick="triggerScraping()" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-play me-1"></i>
                            Run Scraping Now
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button onclick="reloadDashboards()" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-sync me-1"></i>
                            Reload Dashboards
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button onclick="clearDatabase()" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-trash me-1"></i>
                            Clear Old Data
                        </button>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Global Configuration:</strong> These settings apply to all scraping across all dashboards.
                    Changes take effect on the next scraping cycle.
                </div>
            </div>
        </div>

        <!-- Global Account Management -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    Global Account Management
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fab fa-twitter me-2"></i>Twitter Accounts</h6>
                        <div class="input-group mb-2">
                            <input type="text" class="form-control" id="newTwitterAccount" placeholder="@username">
                            <button class="btn btn-outline-primary" onclick="addTwitterAccount()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div id="twitterAccountsList" class="small text-muted">
                            Loading accounts...
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fab fa-facebook me-2"></i>Facebook Pages</h6>
                        <div class="input-group mb-2">
                            <input type="text" class="form-control" id="newFacebookPage" placeholder="Page name">
                            <button class="btn btn-outline-primary" onclick="addFacebookPage()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div id="facebookPagesList" class="small text-muted">
                            Loading pages...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Sidebar -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Scraping Statistics
                </h5>
            </div>
            <div class="card-body">
                {% if scraper_stats %}
                <div class="mb-3">
                    <strong>Total Runs:</strong> {{ scraper_stats.total_runs }}
                </div>
                <div class="mb-3">
                    <strong>Successful:</strong> 
                    <span class="text-success">{{ scraper_stats.successful_runs }}</span>
                </div>
                <div class="mb-3">
                    <strong>Failed:</strong> 
                    <span class="text-danger">{{ scraper_stats.failed_runs }}</span>
                </div>
                
                {% if scraper_stats.last_run %}
                <div class="mb-3">
                    <strong>Last Run:</strong><br>
                    <small class="text-muted">{{ scraper_stats.last_run }}</small>
                </div>
                {% endif %}
                
                {% if scraper_stats.last_success %}
                <div class="mb-3">
                    <strong>Last Success:</strong><br>
                    <small class="text-success">{{ scraper_stats.last_success }}</small>
                </div>
                {% endif %}
                
                {% if scraper_stats.last_error %}
                <div class="mb-3">
                    <strong>Last Error:</strong><br>
                    <small class="text-danger">{{ scraper_stats.last_error }}</small>
                </div>
                {% endif %}
                
                <button onclick="resetStats()" class="btn btn-outline-secondary btn-sm w-100">
                    <i class="fas fa-redo me-1"></i>
                    Reset Statistics
                </button>
                {% else %}
                <p class="text-muted">No statistics available yet.</p>
                {% endif %}
            </div>
        </div>
        
        <!-- System Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Platform:</strong> Windows
                </div>
                <div class="mb-2">
                    <strong>Database:</strong> SQLite
                </div>
                <div class="mb-2">
                    <strong>Scraping Interval:</strong> 5 minutes
                </div>
                <div class="mb-2">
                    <strong>Data Retention:</strong> 7 days
                </div>
                
                <hr>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Note:</strong> This tool uses web scraping without official APIs. 
                    If scraping fails, mock data will be generated to keep the dashboard functional.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Load configuration on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadConfiguration();
        loadAccounts();
    });

    function loadConfiguration() {
        fetch('/api/settings/config')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const config = data.config;
                    const timing = config.timing || {};

                    // Load timing settings
                    document.getElementById('delayBetweenAccounts').value = timing.delay_between_accounts || 3;
                    document.getElementById('delayBetweenPlatforms').value = timing.delay_between_platforms || 15;
                    document.getElementById('delayAfterComplete').value = timing.delay_after_all_platforms_complete || 30;
                    document.getElementById('scrapingInterval').value = timing.scraping_interval_minutes || 5;

                    // Load platform settings
                    const platforms = config.platforms || {};
                    document.getElementById('twitterEnabled').checked = platforms.twitter?.enabled !== false;
                    document.getElementById('facebookEnabled').checked = platforms.facebook?.enabled !== false;
                }
            })
            .catch(error => console.error('Error loading configuration:', error));
    }

    function loadAccounts() {
        fetch('/api/settings/accounts')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const accounts = data.accounts;

                    // Display Twitter accounts
                    const twitterList = document.getElementById('twitterAccountsList');
                    if (accounts.twitter && accounts.twitter.length > 0) {
                        twitterList.innerHTML = accounts.twitter.map(account =>
                            `<span class="badge bg-primary me-1">@${account}</span>`
                        ).join('');
                    } else {
                        twitterList.innerHTML = '<em>No Twitter accounts configured</em>';
                    }

                    // Display Facebook pages
                    const facebookList = document.getElementById('facebookPagesList');
                    if (accounts.facebook && accounts.facebook.length > 0) {
                        facebookList.innerHTML = accounts.facebook.map(page =>
                            `<span class="badge bg-primary me-1">${page}</span>`
                        ).join('');
                    } else {
                        facebookList.innerHTML = '<em>No Facebook pages configured</em>';
                    }
                }
            })
            .catch(error => console.error('Error loading accounts:', error));
    }

    function updateTimingConfig() {
        const timingData = {
            delay_between_accounts: parseFloat(document.getElementById('delayBetweenAccounts').value),
            delay_between_platforms: parseFloat(document.getElementById('delayBetweenPlatforms').value),
            delay_after_all_platforms_complete: parseFloat(document.getElementById('delayAfterComplete').value),
            scraping_interval_minutes: parseInt(document.getElementById('scrapingInterval').value)
        };

        fetch('/api/settings/timing', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(timingData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Timing configuration updated successfully!');
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating timing configuration');
        });
    }

    function addTwitterAccount() {
        const username = document.getElementById('newTwitterAccount').value.trim();
        if (!username) {
            alert('Please enter a Twitter username');
            return;
        }

        fetch('/api/settings/accounts/twitter', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username: username })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('newTwitterAccount').value = '';
                loadAccounts(); // Reload the accounts list
                alert('Twitter account added successfully!');
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error adding Twitter account');
        });
    }

    function addFacebookPage() {
        const pageName = document.getElementById('newFacebookPage').value.trim();
        if (!pageName) {
            alert('Please enter a Facebook page name');
            return;
        }

        fetch('/api/settings/accounts/facebook', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ page_name: pageName })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('newFacebookPage').value = '';
                loadAccounts(); // Reload the accounts list
                alert('Facebook page added successfully!');
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error adding Facebook page');
        });
    }

    function reloadDashboards() {
        fetch('/api/settings/reload-dashboards', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadAccounts(); // Reload the accounts list
                alert('Dashboards reloaded successfully!');
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error reloading dashboards');
        });
    }

    function triggerScraping() {
        fetch('/api/trigger-scraping', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Scraping triggered successfully!');
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error triggering scraping');
        });
    }

    function clearDatabase() {
        if (confirm('Are you sure you want to clear old data? This action cannot be undone.')) {
            fetch('/api/clear-database', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Database cleared successfully!');
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error clearing database');
            });
        }
    }

    // Handle platform enable/disable
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('twitterEnabled').addEventListener('change', function() {
            updatePlatformEnabled('twitter', this.checked);
        });

        document.getElementById('facebookEnabled').addEventListener('change', function() {
            updatePlatformEnabled('facebook', this.checked);
        });
    });

    function updatePlatformEnabled(platform, enabled) {
        fetch(`/api/settings/platforms/${platform}/enabled`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enabled: enabled })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log(`${platform} ${enabled ? 'enabled' : 'disabled'}`);
            } else {
                alert('Error: ' + data.error);
                // Revert the checkbox state
                document.getElementById(platform + 'Enabled').checked = !enabled;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert(`Error updating ${platform} settings`);
            // Revert the checkbox state
            document.getElementById(platform + 'Enabled').checked = !enabled;
        });
    }

    function resetStats() {
        if (confirm('Reset all scraping statistics?')) {
            alert('Statistics reset (feature not implemented yet)');
            // Here you would make an API call to reset stats
        }
    }
</script>
{% endblock %}
