{% extends "base.html" %}

{% block title %}Trending Posts - Social Media Monitor{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-fire text-danger me-2"></i>
                    Trending Now
                </h5>
                <h3 class="text-primary">{{ stats.trending_count if stats else 0 }}</h3>
                <small class="text-muted">Posts trending in last hour</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-database text-info me-2"></i>
                    Total Posts
                </h5>
                <h3 class="text-primary">{{ stats.total_posts if stats else 0 }}</h3>
                <small class="text-muted">All monitored posts</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fab fa-facebook text-primary me-2"></i>
                    Facebook
                </h5>
                <h3 class="text-primary">{{ stats.platform_counts.facebook if stats and stats.platform_counts else 0 }}</h3>
                <small class="text-muted">Facebook posts</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fab fa-twitter text-info me-2"></i>
                    Twitter
                </h5>
                <h3 class="text-primary">{{ stats.platform_counts.twitter if stats and stats.platform_counts else 0 }}</h3>
                <small class="text-muted">Twitter posts</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Form -->
<div class="search-form">
    <form method="GET" action="{{ url_for('dashboard.trending') }}">
        <div class="row">
            <div class="col-md-4">
                <label for="keyword" class="form-label">
                    <i class="fas fa-search me-1"></i>
                    Keyword Search
                </label>
                <input type="text" class="form-control" id="keyword" name="keyword" 
                       value="{{ current_keyword }}" placeholder="Enter keyword to search...">
            </div>
            <div class="col-md-2">
                <label for="platform" class="form-label">
                    <i class="fas fa-filter me-1"></i>
                    Platform
                </label>
                <select class="form-select" id="platform" name="platform">
                    <option value="">All Platforms</option>
                    <option value="facebook" {% if current_platform == 'facebook' %}selected{% endif %}>Facebook</option>
                    <option value="twitter" {% if current_platform == 'twitter' %}selected{% endif %}>Twitter</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="sort_by" class="form-label">
                    <i class="fas fa-sort me-1"></i>
                    Sort By
                </label>
                <select class="form-select" id="sort_by" name="sort_by">
                    <option value="velocity" {% if current_sort == 'velocity' %}selected{% endif %}>Velocity</option>
                    <option value="timestamp" {% if current_sort == 'timestamp' %}selected{% endif %}>Time</option>
                    <option value="engagement" {% if current_sort == 'engagement' %}selected{% endif %}>Engagement</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="limit" class="form-label">
                    <i class="fas fa-list me-1"></i>
                    Limit
                </label>
                <select class="form-select" id="limit" name="limit">
                    <option value="25" {% if current_limit == 25 %}selected{% endif %}>25</option>
                    <option value="50" {% if current_limit == 50 %}selected{% endif %}>50</option>
                    <option value="100" {% if current_limit == 100 %}selected{% endif %}>100</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        Search
                    </button>
                </div>
            </div>
        </div>
    </form>
    
    <div class="row mt-3">
        <div class="col-md-6">
            <a href="{{ url_for('dashboard.trending') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-times me-1"></i>
                Clear Filters
            </a>
        </div>
        <div class="col-md-6 text-end">
            <button onclick="manualRefresh()" class="btn btn-outline-primary btn-sm me-2">
                <i class="fas fa-sync-alt me-1"></i>
                Refresh
            </button>
            <button onclick="triggerScraping()" class="btn btn-outline-success btn-sm">
                <i class="fas fa-play me-1"></i>
                Run Scraping
            </button>
        </div>
    </div>
</div>

<!-- Results Header -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <h4>
        {% if current_keyword %}
            Search Results for "{{ current_keyword }}"
        {% elif current_sort == 'velocity' %}
            Trending Posts
        {% else %}
            Recent Posts
        {% endif %}
    </h4>
    <span class="badge bg-secondary">{{ posts|length }} posts</span>
</div>

<!-- Posts List -->
{% if posts %}
    <div class="row">
        {% for post in posts %}
        <div class="col-12 mb-3">
            <div class="card post-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="d-flex align-items-center">
                            <!-- Platform Badge -->
                            <span class="badge platform-badge platform-{{ post.platform }} me-2">
                                {% if post.platform == 'facebook' %}
                                    <i class="fab fa-facebook me-1"></i>Facebook
                                {% elif post.platform == 'twitter' %}
                                    <i class="fab fa-twitter me-1"></i>Twitter
                                {% else %}
                                    {{ post.platform|title }}
                                {% endif %}
                            </span>
                            
                            <!-- Trending Badge -->
                            {% if post.is_trending %}
                                <span class="badge trending-badge me-2">
                                    <i class="fas fa-fire me-1"></i>TRENDING
                                </span>
                            {% endif %}
                            
                            <!-- Author -->
                            <strong>{{ post.author }}</strong>
                        </div>
                        
                        <div class="text-end">
                            <div class="time-ago">{{ post.time_ago }}</div>
                            {% if post.velocity_score > 0 %}
                                <div class="velocity-score">
                                    Velocity: {{ post.velocity_score }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Post Content -->
                    <div class="post-content mb-3">
                        <p class="mb-2">{{ post.content }}</p>
                        {% if post.url %}
                            <a href="{{ post.url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-external-link-alt me-1"></i>
                                View Original
                            </a>
                        {% endif %}
                    </div>
                    
                    <!-- Engagement Stats -->
                    <div class="engagement-stats">
                        <i class="fas fa-chart-bar me-1"></i>
                        {{ post.engagement_text }}
                        {% if post.total_engagement > 0 %}
                            • Total: {{ post.total_engagement }}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No posts found</h4>
        <p class="text-muted">
            {% if current_keyword %}
                No posts match your search criteria. Try different keywords or remove filters.
            {% else %}
                No posts available yet. The scraping process may still be running.
            {% endif %}
        </p>
        <button onclick="triggerScraping()" class="btn btn-primary">
            <i class="fas fa-play me-1"></i>
            Start Scraping
        </button>
    </div>
{% endif %}

<!-- Load More Button (if needed) -->
{% if posts and posts|length >= current_limit %}
<div class="text-center mt-4">
    <p class="text-muted">Showing {{ posts|length }} posts. Increase the limit to see more.</p>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form when filters change
    document.getElementById('platform').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('sort_by').addEventListener('change', function() {
        this.form.submit();
    });
    
    document.getElementById('limit').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Highlight search terms
    function highlightSearchTerms() {
        const keyword = '{{ current_keyword }}';
        if (keyword) {
            const posts = document.querySelectorAll('.post-content p');
            posts.forEach(post => {
                const regex = new RegExp(`(${keyword})`, 'gi');
                post.innerHTML = post.innerHTML.replace(regex, '<mark>$1</mark>');
            });
        }
    }
    
    // Run highlighting when page loads
    document.addEventListener('DOMContentLoaded', highlightSearchTerms);
</script>
{% endblock %}
